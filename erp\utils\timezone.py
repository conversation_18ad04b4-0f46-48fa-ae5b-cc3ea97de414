"""
Timezone utilities for ERP system

This module provides timezone-related functionality including:
- Dynamic timezone selection using Python's timezone data
- Timezone validation and conversion utilities
- Common timezone lists for UI components
"""

import sys
from typing import List, Tuple, Optional
from datetime import datetime, timezone as dt_timezone

# Try to import zoneinfo (Python 3.9+), fallback to pytz
try:
    from zoneinfo import ZoneInfo, available_timezones
    ZONEINFO_AVAILABLE = True
except ImportError:
    try:
        import pytz
        ZONEINFO_AVAILABLE = False
    except ImportError:
        raise ImportError("Neither zoneinfo nor pytz is available. Please install pytz for Python < 3.9")


def get_fallback_timezones() -> List[str]:
    """
    Get fallback timezone list when zoneinfo/pytz is not available or has no data

    Returns:
        List of common timezone names
    """
    return [
        'UTC',
        'US/Eastern',
        'US/Central',
        'US/Mountain',
        'US/Pacific',
        'Europe/London',
        'Europe/Paris',
        'Europe/Berlin',
        'Europe/Rome',
        'Europe/Madrid',
        'Asia/Tokyo',
        'Asia/Shanghai',
        'Asia/Kolkata',
        'Asia/Dubai',
        'Australia/Sydney',
        'Australia/Melbourne',
        'America/New_York',
        'America/Chicago',
        'America/Denver',
        'America/Los_Angeles',
        'America/Toronto',
        'America/Mexico_City',
        'America/Sao_Paulo',
        'Africa/Cairo',
        'Africa/Johannesburg',
    ]


def get_all_timezones() -> List[str]:
    """
    Get all available timezone names

    Returns:
        List of timezone names sorted alphabetically
    """
    if ZONEINFO_AVAILABLE:
        zones = list(available_timezones())
        if not zones:
            # Fallback to hardcoded list if zoneinfo has no data
            return get_fallback_timezones()
        return sorted(zones)
    else:
        return sorted(list(pytz.all_timezones))


def get_common_timezones() -> List[str]:
    """
    Get commonly used timezone names

    Returns:
        List of common timezone names
    """
    if ZONEINFO_AVAILABLE:
        # For zoneinfo, we'll use our fallback list
        common = get_fallback_timezones()
        # Filter to only include timezones that actually exist
        all_zones = get_all_timezones()
        if all_zones:
            return [tz for tz in common if tz in all_zones]
        else:
            # If no zones available, return the fallback list
            return common
    else:
        try:
            return sorted(list(pytz.common_timezones))
        except:
            return get_fallback_timezones()


def get_timezone_selection() -> List[Tuple[str, str]]:
    """
    Get timezone selection list for use in Selection fields
    
    Returns:
        List of (timezone_name, display_name) tuples
    """
    timezones = get_common_timezones()
    selection = []
    
    for tz_name in timezones:
        # Create a more user-friendly display name
        display_name = tz_name.replace('_', ' ')
        
        # Add UTC offset information for better UX
        try:
            if ZONEINFO_AVAILABLE:
                tz = ZoneInfo(tz_name)
                now = datetime.now(tz)
            else:
                tz = pytz.timezone(tz_name)
                now = datetime.now(tz)
            
            offset = now.strftime('%z')
            if offset:
                # Format offset as +/-HH:MM
                offset_formatted = f"{offset[:3]}:{offset[3:]}"
                display_name = f"{display_name} (UTC{offset_formatted})"
        except Exception:
            # If we can't get offset info, just use the name
            pass
        
        selection.append((tz_name, display_name))
    
    return selection


def validate_timezone(timezone_name: str) -> bool:
    """
    Validate if a timezone name is valid
    
    Args:
        timezone_name: Timezone name to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not timezone_name:
        return False
        
    try:
        if ZONEINFO_AVAILABLE:
            ZoneInfo(timezone_name)
        else:
            pytz.timezone(timezone_name)
        return True
    except Exception:
        return False


def get_timezone_offset(timezone_name: str, dt: Optional[datetime] = None) -> Optional[str]:
    """
    Get timezone offset for a given timezone and datetime
    
    Args:
        timezone_name: Timezone name
        dt: Datetime to get offset for (defaults to now)
        
    Returns:
        Offset string in format '+/-HH:MM' or None if invalid
    """
    if not validate_timezone(timezone_name):
        return None
        
    if dt is None:
        dt = datetime.now()
        
    try:
        if ZONEINFO_AVAILABLE:
            tz = ZoneInfo(timezone_name)
            localized_dt = dt.replace(tzinfo=tz)
        else:
            tz = pytz.timezone(timezone_name)
            localized_dt = tz.localize(dt)
            
        offset = localized_dt.strftime('%z')
        if offset:
            return f"{offset[:3]}:{offset[3:]}"
    except Exception:
        pass
        
    return None


def convert_timezone(dt: datetime, from_tz: str, to_tz: str) -> Optional[datetime]:
    """
    Convert datetime from one timezone to another
    
    Args:
        dt: Datetime to convert
        from_tz: Source timezone name
        to_tz: Target timezone name
        
    Returns:
        Converted datetime or None if conversion fails
    """
    if not validate_timezone(from_tz) or not validate_timezone(to_tz):
        return None
        
    try:
        if ZONEINFO_AVAILABLE:
            from_zone = ZoneInfo(from_tz)
            to_zone = ZoneInfo(to_tz)
            
            # If datetime is naive, assume it's in from_tz
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=from_zone)
            
            return dt.astimezone(to_zone)
        else:
            from_zone = pytz.timezone(from_tz)
            to_zone = pytz.timezone(to_tz)
            
            # If datetime is naive, localize it to from_tz
            if dt.tzinfo is None:
                dt = from_zone.localize(dt)
            
            return dt.astimezone(to_zone)
    except Exception:
        return None


def get_user_timezone_display(timezone_name: str) -> str:
    """
    Get a user-friendly display string for a timezone
    
    Args:
        timezone_name: Timezone name
        
    Returns:
        User-friendly timezone display string
    """
    if not timezone_name:
        return 'UTC'
        
    display_name = timezone_name.replace('_', ' ')
    offset = get_timezone_offset(timezone_name)
    
    if offset:
        return f"{display_name} (UTC{offset})"
    
    return display_name


# Lambda function for dynamic timezone selection
def get_timezone_selection_lambda():
    """
    Lambda function that returns timezone selection for use in Selection fields
    This can be used as: Selection(selection=get_timezone_selection_lambda())
    """
    return get_timezone_selection()
