"""
Related field implementation
"""
from .base import RelationalField


class Related(RelationalField):
    """Related field - computed field that follows relationships"""

    def __init__(self, related, **kwargs):
        """
        Initialize Related field

        Args:
            related: Dot-separated path to the related field (e.g., 'partner_id.name')
        """
        # Related fields are computed and not stored by default
        kwargs.setdefault('compute', True)
        kwargs.setdefault('store', False)
        kwargs.setdefault('readonly', True)

        # Extract comodel_name from the related path (first part)
        related_path = related.split('.')
        # TODO: Determine comodel_name from model registry when available
        comodel_name = 'related'

        super().__init__(comodel_name, **kwargs)
        self.related = related
        self.related_path = related_path

    def get_sql_type(self):
        """Related fields don't have SQL representation unless stored"""
        if self.store:
            # TODO: Determine SQL type from target field when model registry is available
            return "TEXT"
        return None

    def _validate_value(self, value):
        """Related fields are computed, so validation depends on target field"""
        # The validation will be handled by the target field
        return value

    def compute_value(self, record):
        """Compute the related field value by following the path"""
        current = record

        try:
            for step in self.related_path:
                if hasattr(current, step):
                    current = getattr(current, step)
                else:
                    return None

                # If we hit None at any point, return None
                if current is None:
                    return None

            return current

        except (AttributeError, TypeError):
            return None

    def convert_to_cache(self, value):
        """Convert value for caching"""
        return value

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        return value
