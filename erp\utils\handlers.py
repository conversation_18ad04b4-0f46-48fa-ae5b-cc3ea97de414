"""
Request handlers and validators for ERP system
"""
from typing import Any, Dict, Optional, List
from fastapi import Request, HTTPException
from pydantic import BaseModel, ValidationError

from ..logging import get_logger

logger = get_logger(__name__)


class ModelRequestHandler:
    """Handler for model-related requests"""
    
    def __init__(self, model_registry=None):
        self.model_registry = model_registry
    
    async def handle_create(self, model_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle model create request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")
        
        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        try:
            result = await model.create(data)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error creating {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def handle_read(self, model_name: str, record_ids: List[str], fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """Handle model read request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")
        
        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        try:
            result = await model.browse(record_ids).read(fields)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error reading {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def handle_write(self, model_name: str, record_ids: List[str], data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle model write request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")
        
        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        try:
            result = await model.browse(record_ids).write(data)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error writing {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def handle_unlink(self, model_name: str, record_ids: List[str]) -> Dict[str, Any]:
        """Handle model unlink request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")
        
        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        try:
            result = await model.browse(record_ids).unlink()
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error unlinking {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def handle_search(self, model_name: str, domain: List = None, limit: int = None, offset: int = 0) -> Dict[str, Any]:
        """Handle model search request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")
        
        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        try:
            result = await model.search(domain or [], limit=limit, offset=offset)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error searching {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))


class RequestValidator:
    """Validator for incoming requests"""
    
    @staticmethod
    def validate_model_name(model_name: str) -> bool:
        """Validate model name format"""
        if not model_name:
            return False
        
        # Model names should contain only letters, numbers, dots, and underscores
        import re
        pattern = r'^[a-zA-Z][a-zA-Z0-9._]*$'
        return bool(re.match(pattern, model_name))
    
    @staticmethod
    def validate_record_ids(record_ids: Any) -> List[str]:
        """Validate and normalize record IDs"""
        if not record_ids:
            raise HTTPException(status_code=400, detail="Record IDs are required")
        
        if isinstance(record_ids, str):
            record_ids = [record_ids]
        elif not isinstance(record_ids, list):
            raise HTTPException(status_code=400, detail="Record IDs must be a string or list of strings")
        
        # Validate each ID
        validated_ids = []
        for record_id in record_ids:
            if not isinstance(record_id, str):
                record_id = str(record_id)
            
            if not record_id.strip():
                raise HTTPException(status_code=400, detail="Empty record ID not allowed")
            
            validated_ids.append(record_id.strip())
        
        return validated_ids
    
    @staticmethod
    def validate_domain(domain: Any) -> List:
        """Validate domain format"""
        if domain is None:
            return []
        
        if not isinstance(domain, list):
            raise HTTPException(status_code=400, detail="Domain must be a list")
        
        # Basic domain validation - each item should be a tuple/list with 3 elements
        for item in domain:
            if not isinstance(item, (list, tuple)) or len(item) != 3:
                raise HTTPException(
                    status_code=400, 
                    detail="Domain items must be tuples/lists with 3 elements: (field, operator, value)"
                )
        
        return domain
    
    @staticmethod
    def validate_fields(fields: Any) -> Optional[List[str]]:
        """Validate fields list"""
        if fields is None:
            return None
        
        if isinstance(fields, str):
            fields = [fields]
        elif not isinstance(fields, list):
            raise HTTPException(status_code=400, detail="Fields must be a string or list of strings")
        
        # Validate each field name
        validated_fields = []
        for field in fields:
            if not isinstance(field, str):
                raise HTTPException(status_code=400, detail="Field names must be strings")
            
            if not field.strip():
                raise HTTPException(status_code=400, detail="Empty field name not allowed")
            
            validated_fields.append(field.strip())
        
        return validated_fields
    
    @staticmethod
    def validate_pagination(limit: Any, offset: Any) -> tuple:
        """Validate pagination parameters"""
        if limit is not None:
            try:
                limit = int(limit)
                if limit < 0:
                    raise HTTPException(status_code=400, detail="Limit must be non-negative")
                if limit > 10000:  # Reasonable upper limit
                    raise HTTPException(status_code=400, detail="Limit too large (max 10000)")
            except (ValueError, TypeError):
                raise HTTPException(status_code=400, detail="Limit must be an integer")
        
        if offset is not None:
            try:
                offset = int(offset)
                if offset < 0:
                    raise HTTPException(status_code=400, detail="Offset must be non-negative")
            except (ValueError, TypeError):
                raise HTTPException(status_code=400, detail="Offset must be an integer")
        else:
            offset = 0
        
        return limit, offset



