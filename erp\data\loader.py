"""
Data Loader for ERP system

Loads parsed XML data into the database, handling record creation,
updates, and XML ID management.
"""

import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
import json

from .parser import XMLDataParser
from .exceptions import DataLoadingError, ModelNotFoundError, RecordCreationError, XMLIDError
from ..logging import get_logger
from ..environment import Environment


class DataLoader:
    """Loads XML data into the database"""
    
    def __init__(self, env: Environment):
        self.env = env
        self.parser = XMLDataParser()
        self.logger = get_logger(__name__)
        self._xml_id_cache = {}  # Cache for XML ID to record ID mapping
        
    async def load_data_file(self, file_path: str, addon_name: str = None) -> Dict[str, Any]:
        """
        Load data from an XML file
        
        Args:
            file_path: Path to the XML data file
            addon_name: Name of the addon (for XML ID namespacing)
            
        Returns:
            Dictionary with loading results
        """
        try:
            self.logger.info(f"Loading data file: {file_path}")
            
            # Parse the XML file
            records = self.parser.parse_file(file_path)
            
            # Load records into database
            result = await self._load_records(records, addon_name)
            
            self.logger.info(f"Successfully loaded {result['loaded']} records from {file_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to load data file {file_path}: {e}")
            raise DataLoadingError(f"Failed to load data file {file_path}: {e}")
    
    async def load_data_content(self, xml_content: str, addon_name: str = None) -> Dict[str, Any]:
        """
        Load data from XML content string
        
        Args:
            xml_content: XML content as string
            addon_name: Name of the addon (for XML ID namespacing)
            
        Returns:
            Dictionary with loading results
        """
        try:
            # Parse the XML content
            records = self.parser.parse_content(xml_content)
            
            # Load records into database
            return await self._load_records(records, addon_name)
            
        except Exception as e:
            self.logger.error(f"Failed to load XML content: {e}")
            raise DataLoadingError(f"Failed to load XML content: {e}")
    
    async def _load_records(self, records: List[Dict[str, Any]], addon_name: str = None) -> Dict[str, Any]:
        """Load a list of record definitions into the database"""
        result = {
            'loaded': 0,
            'updated': 0,
            'skipped': 0,
            'errors': []
        }
        
        for record_def in records:
            try:
                success = await self._load_single_record(record_def, addon_name)
                if success:
                    result['loaded'] += 1
                else:
                    result['skipped'] += 1
                    
            except Exception as e:
                error_msg = f"Failed to load record {record_def.get('xml_id', 'unknown')}: {e}"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                
        return result
    
    async def _load_single_record(self, record_def: Dict[str, Any], addon_name: str = None) -> bool:
        """Load a single record into the database"""
        model_name = record_def['model']
        xml_id = record_def.get('xml_id')
        values = record_def['values']
        noupdate = record_def.get('noupdate', False)
        
        # Get the model class
        model = await self._get_model(model_name)
        if not model:
            raise ModelNotFoundError(f"Model {model_name} not found")
        
        # Process field values
        processed_values = await self._process_field_values(values, model)
        
        # Check if record already exists (by XML ID)
        existing_record = None
        if xml_id:
            existing_record = await self._find_record_by_xml_id(xml_id, addon_name)
        
        if existing_record:
            if not noupdate:
                # Update existing record
                await existing_record.write(processed_values)
                self.logger.debug(f"Updated record {xml_id} in {model_name}")
            else:
                self.logger.debug(f"Skipped updating record {xml_id} (noupdate=True)")
            return True
        else:
            # Create new record
            new_record = await model.create(processed_values)
            
            # Store XML ID mapping if provided
            if xml_id:
                await self._store_xml_id_mapping(xml_id, addon_name, model_name, new_record.id)
            
            self.logger.debug(f"Created record {xml_id or 'no-id'} in {model_name}")
            return True
    
    async def _process_field_values(self, values: Dict[str, Any], model) -> Dict[str, Any]:
        """Process field values, handling references and evaluations"""
        processed = {}
        
        for field_name, field_def in values.items():
            if isinstance(field_def, dict):
                field_type = field_def.get('type')
                field_value = field_def.get('value')
                
                if field_type == 'ref':
                    # Reference to another record
                    processed[field_name] = await self._resolve_reference(field_value)
                elif field_type == 'eval':
                    # Python expression to evaluate
                    processed[field_name] = self._evaluate_expression(field_value)
                else:
                    # Text value
                    processed[field_name] = field_value
            else:
                # Direct value
                processed[field_name] = field_def
                
        return processed
    
    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record"""
        # Handle module.xml_id format
        if '.' in ref_value:
            addon_name, xml_id = ref_value.split('.', 1)
        else:
            addon_name = None
            xml_id = ref_value
            
        record = await self._find_record_by_xml_id(xml_id, addon_name)
        return record.id if record else None
    
    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        # For now, handle simple cases
        # In production, this should use a safe evaluation context
        try:
            # Handle common cases
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]  # String literal
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]  # String literal
            else:
                # Try to evaluate as number
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression  # Return as string
        except Exception:
            return expression
    
    async def _get_model(self, model_name: str):
        """Get model class by name"""
        # This would need to be implemented based on your model registry
        # For now, return None to indicate model not found
        try:
            # Try to get model from environment
            return self.env[model_name]
        except KeyError:
            return None
    
    async def _find_record_by_xml_id(self, xml_id: str, addon_name: str = None) -> Optional[Any]:
        """Find a record by its XML ID"""
        # This would query the ir.model.data table in a real implementation
        # For now, return None
        return None
    
    async def _store_xml_id_mapping(self, xml_id: str, addon_name: str, model_name: str, record_id: str):
        """Store XML ID to record ID mapping"""
        # This would store in ir.model.data table in a real implementation
        # For now, just cache it
        full_xml_id = f"{addon_name}.{xml_id}" if addon_name else xml_id
        self._xml_id_cache[full_xml_id] = {
            'model': model_name,
            'record_id': record_id
        }
