"""
Schema generation and comparison utilities for ERP models
"""
import asyncio
import re
from typing import Dict, List, Optional, Any, Tuple

from ..logging import get_logger

# PostgreSQL reserved keywords that need to be quoted
POSTGRESQL_RESERVED_KEYWORDS = {
    'table', 'order', 'group', 'user', 'select', 'from', 'where', 'insert',
    'update', 'delete', 'create', 'drop', 'alter', 'index', 'primary', 'key',
    'foreign', 'references', 'constraint', 'unique', 'not', 'null', 'default',
    'check', 'column', 'database', 'schema', 'view', 'trigger', 'function',
    'procedure', 'sequence', 'grant', 'revoke', 'role', 'transaction', 'commit',
    'rollback', 'begin', 'end', 'if', 'then', 'else', 'case', 'when', 'union',
    'intersect', 'except', 'distinct', 'all', 'any', 'some', 'exists', 'in',
    'between', 'like', 'ilike', 'similar', 'escape', 'is', 'and', 'or', 'having',
    'limit', 'offset', 'fetch', 'for', 'with', 'recursive', 'window', 'over',
    'partition', 'range', 'rows', 'unbounded', 'preceding', 'following', 'current',
    'row', 'exclude', 'ties', 'only', 'lateral', 'cross', 'inner', 'left', 'right',
    'full', 'outer', 'join', 'on', 'using', 'natural', 'as', 'asc', 'desc',
    'nulls', 'first', 'last', 'array', 'type', 'cast', 'extract', 'overlay',
    'placing', 'substring', 'position', 'trim', 'leading', 'trailing', 'both',
    'collate', 'at', 'time', 'zone', 'interval', 'year', 'month', 'day', 'hour',
    'minute', 'second', 'timezone_hour', 'timezone_minute', 'localtime',
    'localtimestamp', 'current_date', 'current_time', 'current_timestamp',
    'current_user', 'session_user', 'system_user', 'authorization',
    'binary', 'bit', 'bit_length', 'both', 'char', 'character', 'character_length',
    'char_length', 'coalesce', 'convert', 'dec', 'decimal', 'exists', 'extract',
    'float', 'global', 'int', 'integer', 'interval', 'local', 'lower', 'match',
    'max', 'min', 'national', 'nchar', 'none', 'nullif', 'numeric', 'octet_length',
    'partial', 'real', 'smallint', 'substring', 'sum', 'translate', 'translation',
    'trim', 'upper', 'varchar', 'varying', 'whenever', 'absolute', 'action',
    'add', 'admin', 'after', 'aggregate', 'alias', 'allocate', 'are', 'assertion',
    'at', 'before', 'binary', 'bit', 'blob', 'boolean', 'breadth', 'call',
    'cascade', 'cascaded', 'catalog', 'clob', 'collation', 'completion',
    'connect', 'connection', 'constraints', 'constructor', 'continue', 'corresponding',
    'cube', 'current_path', 'current_role', 'cycle', 'data', 'date', 'deallocate',
    'declare', 'default', 'deferrable', 'deferred', 'depth', 'deref', 'describe',
    'descriptor', 'destroy', 'destructor', 'deterministic', 'dictionary',
    'diagnostics', 'disconnect', 'domain', 'dynamic', 'each', 'equals', 'every',
    'exception', 'external', 'false', 'first', 'found', 'free', 'general', 'get',
    'global', 'go', 'grouping', 'host', 'identity', 'ignore', 'immediate',
    'indicator', 'initialize', 'initially', 'inout', 'input', 'intersect',
    'isolation', 'iterate', 'language', 'large', 'last', 'lateral', 'leading',
    'less', 'level', 'limit', 'local', 'locator', 'map', 'match', 'modify',
    'module', 'names', 'natural', 'nclob', 'new', 'next', 'no', 'none',
    'normalize', 'object', 'off', 'old', 'operation', 'ordinality', 'out',
    'output', 'pad', 'parameter', 'parameters', 'partial', 'path', 'postfix',
    'prefix', 'preorder', 'prepare', 'preserve', 'prior', 'privileges', 'public',
    'read', 'reads', 'recursive', 'ref', 'referencing', 'relative', 'restrict',
    'result', 'return', 'returns', 'role', 'rollup', 'routine', 'row', 'rows',
    'savepoint', 'scope', 'scroll', 'search', 'section', 'session', 'sets',
    'size', 'space', 'specific', 'specifictype', 'sql', 'sqlexception',
    'sqlstate', 'sqlwarning', 'start', 'state', 'statement', 'static', 'structure',
    'system_user', 'temporary', 'terminate', 'than', 'timezone_hour',
    'timezone_minute', 'trailing', 'transaction', 'translation', 'treat',
    'trigger', 'true', 'under', 'unknown', 'unnest', 'usage', 'using', 'value',
    'variable', 'whenever', 'without', 'work', 'write', 'zone'
}

def quote_identifier(identifier: str) -> str:
    """Quote PostgreSQL identifier if it's a reserved keyword or contains special characters"""
    if identifier.lower() in POSTGRESQL_RESERVED_KEYWORDS or not identifier.isidentifier():
        return f'"{identifier}"'
    return identifier


def camel_to_snake_case(name: str) -> str:
    """Convert camelCase to snake_case for database column names"""
    # Insert an underscore before any uppercase letter that follows a lowercase letter or digit
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    # Insert an underscore before any uppercase letter that follows a lowercase letter
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


class SchemaGenerator:
    """Utility for generating database schema from model definitions"""

    logger = get_logger(__name__)

    @classmethod
    def get_model_schema(cls, model_name: str, model_module_map: Dict[str, str] = None,
                        class_name_map: Dict[str, str] = None) -> Optional[Dict[str, Any]]:
        """
        Generate schema for a model given its name by dynamically loading the model

        Args:
            model_name: Technical name of the model (e.g., 'ir.model')
            model_module_map: Optional mapping of model names to module paths
            class_name_map: Optional mapping of model names to class names

        Returns:
            Dictionary containing schema information or None if model not found
        """
        import importlib
        import sys

        # Use provided mappings or try to discover the model
        if model_module_map:
            module_path = model_module_map.get(model_name)
        else:
            # Try to discover model from registry or other sources
            module_path = cls._discover_model_module_path(model_name)

        if not module_path:
            cls.logger.error(f"No module mapping found for model: {model_name}")
            return None

        try:
            # Import the module to load the model class
            module = importlib.import_module(module_path)

            # Find the model class in the module
            model_class = None

            if class_name_map:
                class_name = class_name_map.get(model_name)
                if class_name and hasattr(module, class_name):
                    model_class = getattr(module, class_name)

            if not model_class:
                # Try to find any class that has the right _name
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (hasattr(attr, '_name') and
                        getattr(attr, '_name') == model_name and
                        hasattr(attr, '__bases__')):
                        model_class = attr
                        break

            if not model_class:
                cls.logger.error(f"Model class not found for: {model_name}")
                return None

            # Extract schema from model class
            schema = cls._extract_schema_from_model_class(model_class)

            # Unload the module to avoid keeping it in memory
            if module_path in sys.modules:
                del sys.modules[module_path]

            return schema

        except Exception as e:
            cls.logger.error(f"Error loading model {model_name}: {e}")
            return None

    @classmethod
    def _discover_model_module_path(cls, model_name: str) -> Optional[str]:
        """
        Try to discover the module path for a model.
        This is a fallback when no explicit mapping is provided.
        """
        # This could be enhanced to use model registry or other discovery mechanisms
        # For now, return None to indicate no discovery mechanism available
        return None

    @classmethod
    def generate_create_table_sql(cls, model_name: str, model_module_map: Dict[str, str] = None,
                                 class_name_map: Dict[str, str] = None) -> Optional[str]:
        """
        Generate CREATE TABLE SQL statement for a model

        Args:
            model_name: Technical name of the model
            model_module_map: Optional mapping of model names to module paths
            class_name_map: Optional mapping of model names to class names

        Returns:
            SQL CREATE TABLE statement or None if model not found
        """
        schema = cls.get_model_schema(model_name, model_module_map, class_name_map)
        if not schema:
            return None

        table_name = schema['table_name']
        columns = []

        # Add field columns
        constraints = []
        for field_name, field_info in schema['fields'].items():
            sql_type = field_info['type']
            if not sql_type:  # Skip fields without SQL representation
                continue

            # Convert camelCase field names to snake_case for database columns
            db_column_name = camel_to_snake_case(field_name)
            quoted_field_name = quote_identifier(db_column_name)
            column_def = f"{quoted_field_name} {sql_type}"

            if field_info.get('required', False):
                column_def += " NOT NULL"

            if field_info.get('default') is not None:
                default_val = field_info['default']
                if isinstance(default_val, str) and not default_val.startswith(('CURRENT_TIMESTAMP', 'gen_random_uuid()', "'", 'TRUE', 'FALSE')):
                    column_def += f" DEFAULT '{default_val}'"
                else:
                    column_def += f" DEFAULT {default_val}"

            # Add unique constraint if specified
            if field_info.get('unique', False):
                column_def += " UNIQUE"

            columns.append(column_def)

            # Collect foreign key constraints
            if 'foreign_key' in field_info:
                fk_info = field_info['foreign_key']
                on_delete_map = {
                    'cascade': 'CASCADE',
                    'set null': 'SET NULL',
                    'restrict': 'RESTRICT',
                    'no action': 'NO ACTION'
                }
                on_delete = on_delete_map.get(fk_info['on_delete'].lower(), 'SET NULL')

                constraint_name = f"fk_{table_name}_{field_name}"
                constraint = (
                    f"CONSTRAINT {constraint_name} FOREIGN KEY ({quoted_field_name}) "
                    f"REFERENCES {fk_info['references_table']} ({fk_info['references_column']}) "
                    f"ON DELETE {on_delete}"
                )
                constraints.append(constraint)

        # Add primary key
        columns.append("PRIMARY KEY (id)")

        # Add constraints to columns
        all_definitions = columns + constraints

        sql = f"""CREATE TABLE IF NOT EXISTS {table_name} (
    {',\n    '.join(all_definitions)}
);"""

        # Add indexes
        index_sqls = []
        for index in schema['indexes']:
            index_sql = f"CREATE INDEX IF NOT EXISTS {index['name']} ON {table_name} ({', '.join(index['columns'])});"
            index_sqls.append(index_sql)

        if index_sqls:
            sql += "\n\n" + "\n".join(index_sqls)

        return sql

    @classmethod
    def generate_many2many_table_sql(cls, model_name: str, field_name: str, field_obj) -> str:
        """
        Generate SQL for Many2many intersection table

        Args:
            model_name: Name of the model containing the Many2many field
            field_name: Name of the Many2many field
            field_obj: The Many2many field object

        Returns:
            SQL string to create the intersection table
        """
        from erp.fields import Many2Many

        if not isinstance(field_obj, Many2Many):
            raise ValueError("Field must be a Many2Many field")

        table_name = field_obj.get_relation_table_name(model_name)
        col1, col2 = field_obj.get_column_names(model_name)

        # Determine which model is which
        model1_table = model_name.replace('.', '_')
        model2_table = field_obj.comodel_name.replace('.', '_')

        sql = f"""CREATE TABLE IF NOT EXISTS {table_name} (
    {col1} UUID NOT NULL,
    {col2} UUID NOT NULL,
    PRIMARY KEY ({col1}, {col2}),
    FOREIGN KEY ({col1}) REFERENCES {model1_table} (id) ON DELETE CASCADE,
    FOREIGN KEY ({col2}) REFERENCES {model2_table} (id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_{table_name}_{col1} ON {table_name} ({col1});
CREATE INDEX IF NOT EXISTS idx_{table_name}_{col2} ON {table_name} ({col2});"""

        return sql

    @classmethod
    def _extract_schema_from_model_class(cls, model_class) -> Dict[str, Any]:
        """
        Extract schema information from a model class

        Args:
            model_class: The model class to extract schema from

        Returns:
            Dictionary containing schema information
        """
        from erp.fields import Field

        # Get table name
        table_name = getattr(model_class, '_table', model_class._name.replace('.', '_'))

        # Extract fields
        fields = {}
        indexes = []

        # Note: Standard fields (id, createAt, updateAt) are already included in model._fields
        # so we don't need to add them manually

        # Extract fields from model class _fields attribute
        if hasattr(model_class, '_fields'):
            for field_name, field_obj in model_class._fields.items():
                field_info = cls._field_to_schema(field_name, field_obj)
                if field_info:
                    # Special handling for id field
                    if field_name == 'id':
                        field_info['type'] = 'UUID'
                        field_info['primary_key'] = True
                        field_info['default'] = 'gen_random_uuid()'

                    fields[field_name] = field_info

                    # Add index if field is indexed
                    if getattr(field_obj, 'index', False):
                        db_column_name = camel_to_snake_case(field_name)
                        indexes.append({
                            'name': f'idx_{table_name}_{db_column_name}',
                            'columns': [db_column_name]
                        })

        return {
            'table_name': table_name,
            'fields': fields,
            'indexes': indexes
        }

    @classmethod
    def _field_to_schema(cls, field_name: str, field_obj) -> Optional[Dict[str, Any]]:
        """
        Convert a field object to schema information

        Args:
            field_name: Name of the field
            field_obj: Field object instance

        Returns:
            Dictionary containing field schema information
        """
        from erp.fields import (
            Char, Text, Boolean, Selection, Integer, Float, Date, Datetime,
            Many2One, One2Many, Many2Many, One2One, Related, Reference,
            Binary, Json, Html
        )

        field_type = type(field_obj).__name__

        # Map field types to PostgreSQL types
        type_mapping = {
            'Char': 'VARCHAR',
            'Text': 'TEXT',
            'Boolean': 'BOOLEAN',
            'Selection': 'VARCHAR',
            'Integer': 'INTEGER',
            'Float': 'REAL',
            'Date': 'DATE',
            'Datetime': 'TIMESTAMP',
            'Many2One': 'UUID',
            'One2One': 'UUID',
            'Reference': 'VARCHAR',
            'Binary': 'BYTEA',
            'Json': 'JSONB',
            'Html': 'TEXT'
        }

        # Skip fields that don't have SQL representation
        if field_type in ('One2Many', 'Many2Many', 'Related') and not getattr(field_obj, 'store', True):
            return None

        pg_type = type_mapping.get(field_type, 'TEXT')

        # Handle size for VARCHAR fields
        if pg_type == 'VARCHAR' and hasattr(field_obj, 'size') and field_obj.size:
            pg_type = f'VARCHAR({field_obj.size})'
        elif pg_type == 'VARCHAR' and field_type == 'Char':
            # Default size for Char fields
            pg_type = 'VARCHAR(255)'
        elif pg_type == 'VARCHAR' and field_type == 'Reference':
            # Reference fields need enough space for "model,id"
            pg_type = 'VARCHAR(255)'

        schema_info = {
            'name': field_name,
            'type': pg_type,
            'required': getattr(field_obj, 'required', False),
            'unique': getattr(field_obj, 'unique', False),
            'primary_key': False
        }

        # Add relational field information
        if field_type in ('Many2One', 'One2One'):
            schema_info['foreign_key'] = {
                'references_table': field_obj.comodel_name.replace('.', '_'),
                'references_column': 'id',
                'on_delete': getattr(field_obj, 'ondelete', 'set null')
            }
            if field_type == 'One2One':
                schema_info['unique'] = True

        elif field_type == 'Many2Many':
            # Many2Many fields need intersection table info
            schema_info['many2many'] = {
                'comodel_name': field_obj.comodel_name,
                'relation_table': getattr(field_obj, 'relation_table', None),
                'column1': getattr(field_obj, 'column1', None),
                'column2': getattr(field_obj, 'column2', None)
            }

        # Add default value if specified
        if hasattr(field_obj, 'default') and field_obj.default is not None:
            if callable(field_obj.default):
                # For callable defaults (like lambda functions), try to evaluate them
                try:
                    default_value = field_obj.default()
                    if isinstance(default_value, str):
                        schema_info['default'] = f"'{default_value}'"
                    elif isinstance(default_value, bool):
                        schema_info['default'] = str(default_value).upper()
                    else:
                        schema_info['default'] = str(default_value)
                except:
                    # If evaluation fails, skip the default
                    pass
            elif isinstance(field_obj.default, str):
                schema_info['default'] = f"'{field_obj.default}'"
            elif isinstance(field_obj.default, bool):
                schema_info['default'] = str(field_obj.default).upper()
            else:
                schema_info['default'] = str(field_obj.default)

        return schema_info

    @classmethod
    async def generate_model_tables(cls, db_manager: 'DatabaseManager', model_names: List[str],
                                   model_module_map: Dict[str, str] = None,
                                   class_name_map: Dict[str, str] = None) -> Dict[str, bool]:
        """
        Generate database tables for specified models

        Args:
            db_manager: Database manager instance
            model_names: List of model names to generate tables for
            model_module_map: Optional mapping of model names to module paths
            class_name_map: Optional mapping of model names to class names

        Returns:
            Dictionary mapping model names to success status
        """
        results = {}

        cls.logger.info(f"Generating tables for {len(model_names)} models...")

        created_count = 0
        for model_name in model_names:
            try:
                sql = cls.generate_create_table_sql(model_name, model_module_map, class_name_map)
                if sql:
                    cls.logger.debug(f"Generated SQL for {model_name}")
                    await db_manager.execute(sql)
                    cls.logger.debug(f"✓ Created table for model: {model_name}")
                    results[model_name] = True
                    created_count += 1
                else:
                    cls.logger.error(f"✗ Failed to generate SQL for model: {model_name}")
                    results[model_name] = False
            except Exception as e:
                cls.logger.error(f"✗ Error creating table for model {model_name}: {e}")
                if 'sql' in locals():
                    cls.logger.error(f"Problematic SQL was:")
                    cls.logger.error(sql)
                results[model_name] = False

        cls.logger.info(f"✓ Generated {created_count} model tables successfully")
        return results

    @classmethod
    async def generate_model_tables_from_classes(cls, db_manager: 'DatabaseManager',
                                                model_classes: Dict[str, type]) -> Dict[str, bool]:
        """
        Generate database tables for models using model classes directly

        Args:
            db_manager: Database manager instance
            model_classes: Dictionary mapping model names to model classes

        Returns:
            Dictionary mapping model names to success status
        """
        results = {}

        cls.logger.info(f"Generating tables for {len(model_classes)} models from classes...")

        created_count = 0
        for model_name, model_class in model_classes.items():
            try:
                sql = cls.generate_create_table_sql_from_class(model_name, model_class)
                if sql:
                    cls.logger.debug(f"Generated SQL for {model_name}")
                    await db_manager.execute(sql)
                    cls.logger.debug(f"✓ Created table for model: {model_name}")
                    results[model_name] = True
                    created_count += 1
                else:
                    cls.logger.error(f"✗ Failed to generate SQL for model: {model_name}")
                    results[model_name] = False
            except Exception as e:
                cls.logger.error(f"✗ Error creating table for model {model_name}: {e}")
                if 'sql' in locals():
                    cls.logger.error(f"Problematic SQL was:")
                    cls.logger.error(sql)
                results[model_name] = False

        cls.logger.info(f"✓ Generated {created_count} model tables from classes successfully")
        return results

    @classmethod
    def _generate_column_definition(cls, field_name: str, field_info: Dict[str, Any]) -> Optional[str]:
        """
        Generate SQL column definition from field information

        Args:
            field_name: Name of the field
            field_info: Dictionary containing field information (type, required, default, etc.)

        Returns:
            SQL column definition string or None if field should be skipped
        """
        sql_type = field_info.get('type')
        if not sql_type:  # Skip fields without SQL representation
            return None

        # Convert camelCase field names to snake_case for database columns
        db_column_name = camel_to_snake_case(field_name)
        quoted_field_name = quote_identifier(db_column_name)
        column_def = f"{quoted_field_name} {sql_type}"

        # Add NOT NULL constraint if required
        if field_info.get('required', False):
            column_def += " NOT NULL"

        # Add default value if specified
        if field_info.get('default') is not None:
            default_val = field_info['default']
            if isinstance(default_val, str) and not default_val.startswith(('CURRENT_TIMESTAMP', 'gen_random_uuid()', "'", 'TRUE', 'FALSE')):
                column_def += f" DEFAULT '{default_val}'"
            else:
                column_def += f" DEFAULT {default_val}"

        # Add unique constraint if specified
        if field_info.get('unique', False):
            column_def += " UNIQUE"

        return column_def

    @classmethod
    def generate_create_table_sql_from_class(cls, model_name: str, model_class: type) -> Optional[str]:
        """
        Generate CREATE TABLE SQL statement for a model using the model class directly

        Args:
            model_name: Technical name of the model
            model_class: The model class

        Returns:
            SQL CREATE TABLE statement or None if model not found
        """
        try:
            schema = cls._extract_schema_from_model_class(model_class)
            if not schema:
                return None

            table_name = schema['table_name']
            columns = []

            for field_name, field_info in schema['fields'].items():
                column_def = cls._generate_column_definition(field_name, field_info)
                if column_def:
                    columns.append(column_def)

            if not columns:
                cls.logger.error(f"No valid columns found for model {model_name}")
                return None

            sql = f"CREATE TABLE IF NOT EXISTS {table_name} (\n"
            sql += ",\n".join(f"    {col}" for col in columns)
            sql += "\n);"

            # Add indexes if any
            if schema.get('indexes'):
                for index in schema['indexes']:
                    index_sql = f"CREATE INDEX IF NOT EXISTS {index['name']} ON {table_name} ({', '.join(index['columns'])})"
                    sql += f"\n{index_sql};"

            return sql

        except Exception as e:
            cls.logger.error(f"Error generating SQL for model class {model_class}: {e}")
            return None

    @classmethod
    async def setup_table_constraints(cls, db_manager: 'DatabaseManager',
                                     constraints: List[Dict] = None,
                                     indexes: List[str] = None) -> bool:
        """
        Set up additional constraints and indexes after table creation

        Args:
            db_manager: Database manager instance
            constraints: List of constraint definitions to add
            indexes: List of index SQL statements to execute

        Returns:
            True if successful, False otherwise
        """
        try:
            # Add unique constraints only if they don't exist
            if constraints:
                for constraint in constraints:
                    # Check if constraint already exists
                    exists = await db_manager.fetchval(
                        """SELECT EXISTS (
                            SELECT 1 FROM information_schema.table_constraints
                            WHERE table_name = $1 AND constraint_name = $2
                        )""",
                        constraint['table'], constraint['constraint_name']
                    )

                    if not exists:
                        await db_manager.execute(constraint['sql'])
                        cls.logger.debug(f"✓ Added constraint {constraint['constraint_name']}")
                    else:
                        cls.logger.debug(f"✓ Constraint {constraint['constraint_name']} already exists")

            # Add indexes (these support IF NOT EXISTS)
            if indexes:
                for index_sql in indexes:
                    await db_manager.execute(index_sql)

            cls.logger.info("✓ Table constraints and indexes set up successfully")
            return True

        except Exception as e:
            cls.logger.error(f"✗ Error setting up table constraints: {e}")
            return False

    @classmethod
    def generate_dynamic_constraints_and_indexes(cls, model_classes: Dict[str, type]) -> tuple:
        """
        Generate constraints and indexes dynamically from model field definitions

        Args:
            model_classes: Dictionary mapping model names to model classes

        Returns:
            Tuple of (constraints_list, indexes_list) in the format expected by setup_table_constraints
        """
        constraints = []
        indexes = []

        for model_name, model_class in model_classes.items():
            if not hasattr(model_class, '_fields'):
                continue

            table_name = getattr(model_class, '_table', model_name.replace('.', '_'))

            # Generate unique constraints from field attributes
            for field_name, field_obj in model_class._fields.items():
                if getattr(field_obj, 'unique', False):
                    db_column_name = camel_to_snake_case(field_name)
                    constraint_name = f'uk_{table_name}_{db_column_name}'
                    constraint = {
                        'table': table_name,
                        'constraint_name': constraint_name,
                        'sql': f'ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} UNIQUE ({db_column_name})'
                    }
                    constraints.append(constraint)

                # Generate indexes from field attributes
                if getattr(field_obj, 'index', False):
                    db_column_name = camel_to_snake_case(field_name)
                    index_name = f'idx_{table_name}_{db_column_name}'
                    index_sql = f'CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({db_column_name})'
                    indexes.append(index_sql)

            # Generate composite constraints from _sql_constraints
            if hasattr(model_class, '_sql_constraints'):
                for constraint_def in model_class._sql_constraints:
                    if len(constraint_def) >= 3:
                        constraint_key, constraint_sql, constraint_msg = constraint_def[:3]
                        constraint_name = f'{constraint_key}_{table_name}'

                        # Parse the constraint SQL to create proper ALTER TABLE statement
                        if constraint_sql.strip().upper().startswith('UNIQUE'):
                            # Extract column list from UNIQUE (col1, col2)
                            columns_part = constraint_sql[constraint_sql.find('('):constraint_sql.rfind(')')+1]
                            constraint = {
                                'table': table_name,
                                'constraint_name': constraint_name,
                                'sql': f'ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} {constraint_sql}'
                            }
                            constraints.append(constraint)

        return constraints, indexes




    @classmethod
    def _pg_type_to_erp_type(cls, pg_type: str) -> str:
        """Convert PostgreSQL type to ERP field type"""
        pg_type_upper = pg_type.upper()

        if pg_type_upper.startswith('VARCHAR'):
            return 'char'
        elif pg_type_upper == 'TEXT':
            return 'text'
        elif pg_type_upper == 'BOOLEAN':
            return 'boolean'
        elif pg_type_upper == 'INTEGER':
            return 'integer'
        elif pg_type_upper == 'REAL':
            return 'float'
        elif pg_type_upper == 'DATE':
            return 'date'
        elif pg_type_upper == 'TIMESTAMP':
            return 'datetime'
        elif pg_type_upper == 'UUID':
            return 'char'  # UUIDs are stored as char fields in ERP
        elif pg_type_upper == 'BYTEA':
            return 'binary'
        elif pg_type_upper == 'JSONB':
            return 'text'  # JSON stored as text in ERP
        else:
            return 'char'  # Default fallback

    @classmethod
    def get_all_models_schema(cls) -> Dict[str, Dict[str, Any]]:
        """
        Get schema for all registered models

        Returns:
            Dictionary mapping model names to their schemas
        """
        # Model registry has been removed - models are no longer automatically registered
        return {}

    @classmethod
    async def sync_and_populate_ir_metadata(cls, db_manager, addon_name: str = None) -> Dict[str, Any]:
        """
        Comprehensive method that performs schema sync followed by IR population.

        This method ensures that IR population only happens after successful schema sync.
        It's designed to be used during addon install/upgrade processes.

        Args:
            db_manager: Database manager instance
            addon_name: Optional addon name for targeted sync/population

        Returns:
            Dictionary containing both sync and population results
        """
        from ..logging.coordination import operation_context

        operation_id = f"schema_sync_ir_{addon_name or 'all'}"
        with operation_context(operation_id, addon=addon_name) as should_execute:
            if not should_execute:
                cls.logger.debug(f"Skipping duplicate schema sync and IR population for {addon_name}")
                return {'status': 'skipped', 'message': 'Duplicate operation prevented'}

            cls.logger.info(f"Starting schema sync and IR population for addon: {addon_name or 'all'}")

            try:
                # Step 1: Perform schema synchronization
                cls.logger.info("Step 1: Synchronizing database schema...")
                sync_results = await SchemaComparator.sync_model_tables(addon_name)

                # Step 2: Check if schema sync was successful
                if not sync_results.get('sync_successful', False):
                    cls.logger.error("Schema synchronization failed - aborting IR population")
                    return {
                        'status': 'error',
                        'message': 'Schema synchronization failed',
                        'schema_sync': sync_results,
                        'ir_population': {
                            'status': 'skipped',
                            'message': 'Skipped due to schema sync failure'
                        }
                    }

                cls.logger.info("✓ Schema synchronization completed successfully")

                # Step 3: Perform IR metadata population
                cls.logger.info("Step 2: Populating IR metadata...")
                from .ir_population import ir_population_manager

                ir_results = await ir_population_manager.populate_ir_metadata(
                    db_manager, addon_name, sync_results
                )

                # Step 4: Compile final results
                overall_status = 'success'
                if sync_results.get('status') == 'error' or ir_results.get('status') == 'error':
                    overall_status = 'error'
                elif sync_results.get('status') == 'partial' or ir_results.get('status') == 'partial':
                    overall_status = 'partial'

                final_results = {
                    'status': overall_status,
                    'message': 'Schema sync and IR population completed',
                    'addon_name': addon_name,
                    'schema_sync': sync_results,
                    'ir_population': ir_results,
                    'summary': {
                        'tables_created': sync_results.get('tables_created', 0),
                        'tables_in_sync': sync_results.get('tables_in_sync', 0),
                        'models_populated': ir_results.get('models_processed', 0),
                        'fields_populated': ir_results.get('fields_processed', 0)
                    }
                }

                cls.logger.info(f"✅ Schema sync and IR population completed: "
                              f"{final_results['summary']['tables_created']} tables created, "
                              f"{final_results['summary']['models_populated']} models populated")

                return final_results

            except Exception as e:
                error_msg = f"Error during schema sync and IR population: {e}"
                cls.logger.error(error_msg)
                return {
                    'status': 'error',
                    'message': error_msg,
                    'addon_name': addon_name,
                    'schema_sync': {'status': 'error', 'message': 'Not attempted'},
                    'ir_population': {'status': 'error', 'message': 'Not attempted'}
                }


class SchemaComparator:
    """Utility for comparing model schemas with database tables"""

    logger = get_logger(__name__)

    @classmethod
    async def get_table_schema(cls, table_name: str) -> Optional[Dict[str, Any]]:
        """
        Get actual table schema from database

        Args:
            table_name: Name of the database table

        Returns:
            Dictionary containing actual table schema or None if table doesn't exist
        """
        if not DatabaseRegistry:
            raise RuntimeError("Database not available")

        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")

        # Check if table exists
        exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = $1
            )
        """
        result = await db.execute(exists_query, (table_name,))
        if not result or not result[0]['exists']:
            return None

        # Get column information
        columns_query = """
            SELECT
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = $1
            ORDER BY ordinal_position
        """
        columns = await db.execute(columns_query, (table_name,))

        # Get indexes
        indexes_query = """
            SELECT
                indexname,
                indexdef
            FROM pg_indexes
            WHERE tablename = $1 AND schemaname = 'public'
        """
        indexes = await db.execute(indexes_query, (table_name,))

        # Get constraints
        constraints_query = """
            SELECT
                conname,
                contype,
                pg_get_constraintdef(oid) as definition
            FROM pg_constraint
            WHERE conrelid = $1::regclass
        """
        constraints = await db.execute(constraints_query, (table_name,))

        schema = {
            'table_name': table_name,
            'columns': {col['column_name']: col for col in columns},
            'indexes': [idx['indexname'] for idx in indexes],
            'constraints': [{'name': c['conname'], 'type': c['contype'], 'definition': c['definition']} for c in constraints]
        }

        return schema



    @classmethod
    async def sync_model_tables(cls, addon_name: str = None, model_registry: 'ModelRegistry' = None) -> Dict[str, Any]:
        """
        Synchronize model tables with their definitions
        Creates missing tables and reports differences

        Enhanced version with better error handling and completion detection
        to ensure IR population only happens after successful sync.

        Args:
            addon_name: Optional addon name to sync only models from specific addon
            model_registry: Optional ModelRegistry instance to use (for lifecycle-bound operations)

        Returns:
            Dictionary containing sync results and statistics with clear success/failure status
        """
        from ..database.registry import DatabaseRegistry

        try:
            db = await DatabaseRegistry.get_current_database()
            if not db:
                return {
                    'status': 'error',
                    'message': 'No database connection available',
                    'sync_successful': False,
                    'errors': ['No database connection available']
                }

            # Use provided model registry or create one for the specific addon
            if model_registry:
                models = model_registry.all()
                cls.logger.info(f"Using provided ModelRegistry with {len(models)} models")
            elif addon_name:
                from ..addons.lifecycle_manager import get_addon_lifecycle_manager
                lifecycle_manager = get_addon_lifecycle_manager()
                temp_registry = lifecycle_manager.create_temporary_model_registry(addon_name)
                models = temp_registry.all()
                cls.logger.info(f"Created temporary ModelRegistry for addon '{addon_name}' with {len(models)} models")
            else:
                # Fallback: process base models only
                models = {}
                cls.logger.warning("No addon specified and no model registry provided - no models to sync")

            results = {
                'status': 'success',
                'sync_successful': True,
                'total_models': len(models),
                'tables_created': 0,
                'tables_updated': 0,
                'tables_in_sync': 0,
                'errors': [],
                'warnings': [],
                'details': {},
                'addon_name': addon_name
            }

            # Track critical errors that should prevent IR population
            critical_errors = []

            for model_name in models.keys():
                try:
                    # Check if table exists
                    table_name = models[model_name]._table or model_name.replace('.', '_')
                    table_exists = await db.fetchval(
                        """SELECT EXISTS (
                           SELECT FROM information_schema.tables
                           WHERE table_schema = 'public'
                           AND table_name = $1
                        )""",
                        table_name
                    )

                    if not table_exists:
                        # Create missing table
                        sql = cls.generate_create_table_sql(model_name)
                        if sql:
                            try:
                                await db.execute(sql)
                                results['tables_created'] += 1
                                results['details'][model_name] = 'created'
                                cls.logger.debug(f"✓ Created table for model {model_name}")
                            except Exception as create_error:
                                error_msg = f"Failed to create table for {model_name}: {create_error}"
                                critical_errors.append(error_msg)
                                results['errors'].append(error_msg)
                                results['details'][model_name] = 'creation_failed'
                        else:
                            error_msg = f"Failed to generate SQL for {model_name}"
                            critical_errors.append(error_msg)
                            results['errors'].append(error_msg)
                            results['details'][model_name] = 'sql_generation_failed'
                    else:
                        # Compare existing table with model definition
                        comparison = await cls.compare_model_with_table(model_name)
                        if comparison.get('status') == 'match':
                            results['tables_in_sync'] += 1
                            results['details'][model_name] = 'in_sync'
                        else:
                            results['tables_updated'] += 1
                            results['details'][model_name] = 'differences_found'
                            # Note: Actual table updates would require more complex logic
                            # For now, we just report the differences as warnings
                            results['warnings'].append(f"Table differences found for {model_name}")

                except Exception as e:
                    error_msg = f"Error processing model {model_name}: {e}"
                    critical_errors.append(error_msg)
                    results['errors'].append(error_msg)
                    results['details'][model_name] = 'error'

            # Determine if sync was successful
            if critical_errors:
                results['status'] = 'error'
                results['sync_successful'] = False
                cls.logger.error(f"Schema sync failed with {len(critical_errors)} critical errors")
            elif results['errors']:
                results['status'] = 'partial'
                results['sync_successful'] = False
                cls.logger.warning(f"Schema sync completed with {len(results['errors'])} errors")
            else:
                results['sync_successful'] = True
                cls.logger.info(f"Schema sync completed successfully: {results['tables_created']} created, "
                              f"{results['tables_in_sync']} in sync, {results['tables_updated']} updated")

            # Clean up temporary registry if we created one
            if not model_registry and addon_name:
                from ..addons.lifecycle_manager import get_addon_lifecycle_manager
                lifecycle_manager = get_addon_lifecycle_manager()
                lifecycle_manager.cleanup_temporary_model_registry(addon_name)

            return results

        except Exception as e:
            error_msg = f"Error during table synchronization: {e}"
            cls.logger.error(error_msg)

            # Clean up temporary registry if we created one
            if not model_registry and addon_name:
                try:
                    from ..addons.lifecycle_manager import get_addon_lifecycle_manager
                    lifecycle_manager = get_addon_lifecycle_manager()
                    lifecycle_manager.cleanup_temporary_model_registry(addon_name)
                except Exception as cleanup_error:
                    cls.logger.warning(f"Failed to cleanup temporary registry for {addon_name}: {cleanup_error}")

            return {
                'status': 'error',
                'sync_successful': False,
                'message': error_msg,
                'errors': [error_msg],
                'addon_name': addon_name
            }



    @classmethod
    async def validate_database_schema(cls, model_registry: 'ModelRegistry' = None) -> Dict[str, Any]:
        """
        Validate that all registered models have corresponding database tables
        with correct structure

        Args:
            model_registry: Optional ModelRegistry instance to validate

        Returns:
            Dictionary containing validation results
        """
        from ..database.registry import DatabaseRegistry

        try:
            db = await DatabaseRegistry.get_current_database()
            if not db:
                return {
                    'status': 'error',
                    'message': 'No database connection available'
                }

            # Use provided model registry or return empty validation
            if model_registry:
                models = model_registry.all()
            else:
                models = {}
                cls.logger.warning("No model registry provided for validation")
            results = {
                'status': 'success',
                'total_models': len(models),
                'valid_tables': 0,
                'missing_tables': 0,
                'invalid_tables': 0,
                'issues': [],
                'details': {}
            }

            for model_name in models.keys():
                try:
                    table_name = models[model_name]._table or model_name.replace('.', '_')

                    # Check if table exists
                    table_exists = await db.fetchval(
                        """SELECT EXISTS (
                           SELECT FROM information_schema.tables
                           WHERE table_schema = 'public'
                           AND table_name = $1
                        )""",
                        table_name
                    )

                    if not table_exists:
                        results['missing_tables'] += 1
                        results['details'][model_name] = 'missing'
                        results['issues'].append(f"Table {table_name} missing for model {model_name}")
                    else:
                        # Validate table structure
                        comparison = await cls.compare_model_with_table(model_name)
                        if comparison.get('status') == 'match':
                            results['valid_tables'] += 1
                            results['details'][model_name] = 'valid'
                        else:
                            results['invalid_tables'] += 1
                            results['details'][model_name] = 'invalid'
                            if 'differences' in comparison:
                                for diff in comparison['differences']:
                                    results['issues'].append(f"{model_name}: {diff}")

                except Exception as e:
                    results['invalid_tables'] += 1
                    results['details'][model_name] = 'error'
                    results['issues'].append(f"Error validating {model_name}: {e}")

            # Determine overall status
            if results['missing_tables'] > 0 or results['invalid_tables'] > 0:
                results['status'] = 'issues_found'

            return results

        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error during schema validation: {e}"
            }

    @classmethod
    def _types_compatible(cls, model_type: str, table_type: str) -> bool:
        """
        Check if model field type is compatible with database column type

        Args:
            model_type: SQL type from model field
            table_type: Actual database column type

        Returns:
            True if types are compatible
        """
        # Normalize types for comparison
        model_type = model_type.upper()
        table_type = table_type.upper()

        # Define type compatibility mappings
        compatible_types = {
            'TEXT': ['TEXT', 'CHARACTER VARYING', 'VARCHAR'],
            'VARCHAR': ['TEXT', 'CHARACTER VARYING', 'VARCHAR'],
            'INTEGER': ['INTEGER', 'BIGINT', 'SMALLINT'],
            'REAL': ['REAL', 'DOUBLE PRECISION', 'NUMERIC'],
            'BOOLEAN': ['BOOLEAN'],
            'DATE': ['DATE'],
            'TIMESTAMP': ['TIMESTAMP', 'TIMESTAMP WITHOUT TIME ZONE', 'TIMESTAMP WITH TIME ZONE'],
        }

        # Extract base type (remove size specifications)
        model_base = model_type.split('(')[0].strip()
        table_base = table_type.split('(')[0].strip()

        # Check compatibility
        for base_type, compatible in compatible_types.items():
            if model_base == base_type:
                return table_base in compatible

        # Default: exact match required
        return model_base == table_base


